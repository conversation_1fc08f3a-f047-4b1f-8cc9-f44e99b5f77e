import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:get/get.dart';
import '../models/dashboard_models.dart';
import '../controllers/dashboard_controller.dart';
import '../services/dashboard_card_export_service.dart';
import 'color_customization_widget.dart';
import 'unified_card_filter_widget.dart';
import 'chart_only_widget.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';

/// ويدجت بطاقة المخطط البياني القابلة للتخصيص
class ChartCardWidget extends StatefulWidget {
  final ChartCardModel card;
  final VoidCallback? onRefresh;
  final Function(String)? onRefreshSingle; // تحديث البطاقة الواحدة
  final Function(ChartType)? onChartTypeChanged;
  final Function(Size)? onSizeChanged;

  const ChartCardWidget({
    super.key,
    required this.card,
    this.onRefresh,
    this.onRefreshSingle,
    this.onChartTypeChanged,
    this.onSizeChanged,
  });

  @override
  State<ChartCardWidget> createState() => _ChartCardWidgetState();
}

class _ChartCardWidgetState extends State<ChartCardWidget> {
  late Size _currentSize;
  bool _isRefreshing = false;
  final ScrollController _headerScrollController = ScrollController();

  @override
  void didUpdateWidget(ChartCardWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // إعادة بناء عند تغيير الألوان أو البيانات أو الفلاتر أو التوقيت
    if (oldWidget.card.colorMapping != widget.card.colorMapping ||
        oldWidget.card.data != widget.card.data ||
        oldWidget.card.cardFilters != widget.card.cardFilters ||
        oldWidget.card.lastUpdated != widget.card.lastUpdated) {

      // إعادة بناء فورية واحدة فقط
      if (mounted) {
        setState(() {});
      }
    }
  }
  bool _isResizing = false;

  @override
  void initState() {
    super.initState();
    _currentSize = widget.card.size;
  }

  @override
  void dispose() {
    _headerScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: _currentSize.width,
      height: _currentSize.height,
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildCardHeader(),
            Expanded(
              child: _buildChartContent(),
            ),
            if (widget.card.isResizable)
              SizedBox(
                height: 20,
                child: _buildResizeHandle(),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء رأس البطاقة
  Widget _buildCardHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Scrollbar(
            controller: _headerScrollController,
            thumbVisibility: true,
            thickness: 4,
            radius: const Radius.circular(2),
            child: SingleChildScrollView(
              controller: _headerScrollController,
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  SizedBox(
                    width: 120, // عرض ثابت للعنوان
                    child: Text(
                      widget.card.title,
                      style: AppStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildChartTypeSelector(),
                  const SizedBox(width: 4),
                  _buildActionButtons(),
                ],
              ),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            widget.card.description,
            style: AppStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
              fontSize: 11,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          _buildDataInfo(),
        ],
      ),
    );
  }

  /// بناء محدد نوع المخطط
  Widget _buildChartTypeSelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: AppColors.border),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<ChartType>(
          value: widget.card.chartType,
          icon: const Icon(Icons.keyboard_arrow_down, size: 14),
          isDense: true,
          items: widget.card.supportedChartTypes.map((type) {
            return DropdownMenuItem<ChartType>(
              value: type,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(_getChartTypeIcon(type), size: 16),
                  const SizedBox(width: 4),
                  Text(
                    _getChartTypeName(type),
                    style: AppStyles.bodySmall,
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (ChartType? newType) {
            if (newType != null && widget.onChartTypeChanged != null) {
              // تحديث فوري للواجهة
              setState(() {
                // سيتم تحديث البطاقة من خلال callback
              });

              widget.onChartTypeChanged!(newType);

              debugPrint('تم تغيير نوع المخطط إلى: $newType');
            }
          },
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return  GetBuilder<DashboardController>(  
      builder: (controller) => 
      Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: _isRefreshing
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.refresh, size: 20),
          onPressed: _isRefreshing ? null : () async {
            setState(() {
              _isRefreshing = true;
              controller.refreshDashboard();
            });

            try {
              // استخدام التحديث الفردي إذا كان متاحاً، وإلا التحديث العام
              if (widget.onRefreshSingle != null) {
                widget.onRefreshSingle!(widget.card.id);
              } else {
                widget.onRefresh?.call();
              }

              // انتظار قصير لإظهار التحديث
              await Future.delayed(const Duration(milliseconds: 800));

              // إجبار إعادة بناء البطاقة بعد التحديث
              if (mounted) {
                setState(() {});
              }
            } finally {
              if (mounted) {
                setState(() => _isRefreshing = false);
              }
            }
          },
          tooltip: 'تحديث هذه البطاقة فقط',
        ),
        IconButton(
          icon: const Icon(Icons.palette, size: 20),
          onPressed: _showColorCustomization,
          tooltip: 'تخصيص الألوان',
        ),
        IconButton(
          icon: Icon(
            Icons.filter_list,
            size: 20,
            color: widget.card.cardFilters.isNotEmpty
                ? AppColors.primary
                : AppColors.textSecondary,
          ),
          onPressed: _showCardFilters,
          tooltip: 'فلاتر البطاقة',
        ),
        IconButton(
          icon: const Icon(Icons.fullscreen, size: 20),
          onPressed: _showFullscreenChart,
          tooltip: 'عرض مكبر',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, size: 20),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download),
                  SizedBox(width: 8),
                  Text('تصدير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings),
                  SizedBox(width: 8),
                  Text('إعدادات'),
                ],
              ),
            ),
          ],
          onSelected: _handleMenuAction,
        ),
      ],
     ),
    );
  }

  /// بناء معلومات البيانات
  Widget _buildDataInfo() {
    return Row(
      children: [
        Icon(
          Icons.data_usage,
          size: 12,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          '${widget.card.data.length} عنصر',
          style: AppStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
            fontSize: 10,
          ),
        ),
        const Spacer(),
        Text(
          'آخر تحديث: ${_formatLastUpdated()}',
          style: AppStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
            fontSize: 10,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  /// بناء محتوى المخطط
  Widget _buildChartContent() {
    if (widget.card.data.isEmpty) {
      return _buildEmptyState();
    }

    return Padding(
      key: ValueKey('chart_content_${widget.card.id}_${widget.card.data.hashCode}_${widget.card.lastUpdated.millisecondsSinceEpoch}'),
      padding: const EdgeInsets.all(16),
      child: _buildChart(),
    );
  }

  /// بناء المخطط حسب النوع
  Widget _buildChart() {
    final chartKey = 'chart_${widget.card.id}_${widget.card.data.hashCode}_${widget.card.lastUpdated.millisecondsSinceEpoch}';

    return GetBuilder<DashboardController>(
      key: ValueKey(chartKey),
      id: 'chart_card_${widget.card.id}',
      builder: (controller) {
        return Container(
          key: ValueKey('${chartKey}_container'),
          child: _buildChartByType(),
        );
      },
    );
  }

  /// بناء المخطط حسب النوع الفعلي
  Widget _buildChartByType() {
    switch (widget.card.chartType) {
      case ChartType.pie:
        return _buildPieChart();
      case ChartType.doughnut:
        return _buildDoughnutChart();
      case ChartType.column:
        return _buildColumnChart();
      case ChartType.bar:
        return _buildBarChart();
      case ChartType.line:
        return _buildLineChart();
      case ChartType.area:
        return _buildAreaChart();
      case ChartType.spline:
        return _buildSplineChart();
      case ChartType.stackedColumn:
        return _buildStackedColumnChart();
      case ChartType.stackedBar:
        return _buildStackedBarChart();
      case ChartType.scatter:
        return _buildScatterChart();
      case ChartType.bubble:
        return _buildBubbleChart();
      case ChartType.funnel:
        return _buildFunnelChart();
      case ChartType.pyramid:
        return _buildPyramidChart();
      case ChartType.stepLine:
        return _buildStepLineChart();
      case ChartType.stackedArea:
        return _buildStackedAreaChart();
      case ChartType.stackedArea100:
        return _buildStackedArea100Chart();
      case ChartType.stackedColumn100:
        return _buildStackedColumn100Chart();
      case ChartType.stackedBar100:
        return _buildStackedBar100Chart();
      case ChartType.splineArea:
        return _buildSplineAreaChart();
      case ChartType.stepArea:
        return _buildStepAreaChart();
      case ChartType.rangeColumn:
        return _buildRangeColumnChart();
      case ChartType.rangeArea:
        return _buildRangeAreaChart();
      case ChartType.waterfall:
        return _buildWaterfallChart();
      case ChartType.radialBar:
        return _buildRadialBarChart();
      case ChartType.fastLine:
        return _buildFastLineChart();
      default:
        return _buildColumnChart();
    }
  }

  /// بناء المخطط الدائري
  Widget _buildPieChart() {
    return SfCircularChart(
      key: ValueKey('${widget.card.id}_${widget.card.colorMapping.hashCode}_${widget.card.data.hashCode}'),
      title: ChartTitle(text: ''),
      legend: const Legend(
        isVisible: true,
        position: LegendPosition.bottom,
        textStyle: TextStyle(fontSize: 12),
      ),
        enableMultiSelection: true,
                  selectionGesture: ActivationMode.singleTap,
      series: <PieSeries<ChartDataPoint, String>>[
        PieSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          pointColorMapper: (ChartDataPoint data, _) => _getPointColor(data),
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.outside,
            textStyle: TextStyle(fontSize: 10),
          ),
          enableTooltip: true,
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
    );
  }

  /// بناء مخطط الدونات
  Widget _buildDoughnutChart() {
    return SfCircularChart(
      key: ValueKey('doughnut_${widget.card.id}_${widget.card.data.hashCode}_${widget.card.lastUpdated.millisecondsSinceEpoch}'),
      title: ChartTitle(text: ''),
      legend: const Legend(
        isVisible: true,
        position: LegendPosition.bottom,
        textStyle: TextStyle(fontSize: 12),
      ),
      series: <DoughnutSeries<ChartDataPoint, String>>[
        DoughnutSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          pointColorMapper: (ChartDataPoint data, _) => _getPointColor(data),
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.outside,
            textStyle: TextStyle(fontSize: 10),
          ),
          enableTooltip: true,
          innerRadius: '60%',
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
    );
  }

  /// بناء مخطط الأعمدة
  Widget _buildColumnChart() {
    return SfCartesianChart(
       
      enableAxisAnimation: true,
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
        labelRotation: -45,
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      series: <ColumnSeries<ChartDataPoint, String>>[
        ColumnSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          pointColorMapper: (ChartDataPoint data, _) => _getPointColor(data),
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(fontSize: 9),
          ),
          enableTooltip: true,
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  /// بناء المخطط الشريطي
  Widget _buildBarChart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      series: <BarSeries<ChartDataPoint, String>>[
        BarSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          pointColorMapper: (ChartDataPoint data, _) => _getPointColor(data),
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(fontSize: 9),
          ),
          enableTooltip: true,
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  /// بناء المخطط الخطي
  Widget _buildLineChart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      series: <LineSeries<ChartDataPoint, String>>[
        LineSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          pointColorMapper: (ChartDataPoint data, _) => _getPointColor(data),
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(fontSize: 9),
          ),
          enableTooltip: true,
          markerSettings: const MarkerSettings(isVisible: true),
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  /// بناء المخطط المساحي
  Widget _buildAreaChart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      series: <AreaSeries<ChartDataPoint, String>>[
        AreaSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          pointColorMapper: (ChartDataPoint data, _) => _getPointColor(data).withValues(alpha: 0.7),
          borderColor: AppColors.primary,
          borderWidth: 2,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(fontSize: 9),
          ),
          enableTooltip: true,
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  /// بناء المخطط المنحني
  Widget _buildSplineChart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      series: <SplineSeries<ChartDataPoint, String>>[
        SplineSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          pointColorMapper: (ChartDataPoint data, _) => _getPointColor(data),
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(fontSize: 9),
          ),
          enableTooltip: true,
          markerSettings: const MarkerSettings(isVisible: true),
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  /// الحصول على لون النقطة المناسب
  Color _getPointColor(ChartDataPoint data) {
    // أولوية للون المحفوظ في البيانات
    if (data.color != null) {
      return data.color!;
    }

    // للبيانات المصفوفة، استخدام category أو استخراج الحالة من label
    String? colorKey;

    if (data.category != null) {
      // استخدام category إذا كان متوفراً (للبيانات المصفوفة)
      colorKey = data.category;
    } else if (data.label.contains(' - ')) {
      // استخراج الحالة من label مثل "أحمد - pending" → "pending"
      colorKey = data.label.split(' - ').last;
    } else {
      // استخدام label كما هو للبيانات العادية
      colorKey = data.label;
    }

    // البحث في colorMapping
    final color = widget.card.colorMapping[colorKey];
    if (color != null) {
      return color;
    }

    // لون افتراضي
    return AppColors.primary;
  }

  /// تحضير بيانات المصفوفة للمخططات المكدسة
  Map<String, Map<String, double>> _prepareMatrixData() {
    final matrixData = <String, Map<String, double>>{};

    try {
      // فلترة البيانات التي تحتوي على metadata للمصفوفة
      final matrixPoints = widget.card.data.where((point) =>
        point.metadata != null &&
        point.metadata!['type'] == 'status_breakdown'
      ).toList();



      if (matrixPoints.isEmpty) {
        debugPrint('⚠️ لا توجد بيانات مصفوفة للمخطط المكدس');
        return matrixData; // إرجاع فارغ إذا لم توجد بيانات مصفوفة
      }

      // تجميع البيانات حسب الحالة والمستخدم
      for (final point in matrixPoints) {
        try {
          final status = point.metadata!['status'] as String? ?? 'غير محدد';
          final user = point.metadata!['parentLabel'] as String? ?? 'غير محدد';
          final count = point.value;

          if (!matrixData.containsKey(status)) {
            matrixData[status] = <String, double>{};
          }

          matrixData[status]![user] = count;
        } catch (e) {
          debugPrint('❌ خطأ في معالجة نقطة بيانات: $e');
          continue; // تجاهل النقطة المعطلة والمتابعة
        }
      }

      debugPrint('🔄 تحضير بيانات المصفوفة: ${matrixData.length} حالة، ${matrixPoints.length} نقطة');
    } catch (e) {
      debugPrint('❌ خطأ في تحضير بيانات المصفوفة: $e');
    }

    return matrixData;
  }

  /// بناء مخطط الأعمدة المكدسة
  Widget _buildStackedColumnChart() {
    try {
      // تجميع البيانات حسب المستخدم والحالة للمخططات المكدسة
      final matrixData = _prepareMatrixData();

      if (matrixData.isEmpty) {
        debugPrint('⚠️ بيانات المصفوفة فارغة، استخدام المخطط العادي');
        return _buildColumnChart(); // fallback للبيانات العادية
      }

      return SfCartesianChart(
        key: ValueKey('stacked_column_${widget.card.id}_${widget.card.lastUpdated.millisecondsSinceEpoch}'),
        primaryXAxis: const CategoryAxis(
          labelStyle: TextStyle(fontSize: 10),
        ),
        primaryYAxis: const NumericAxis(
          labelStyle: TextStyle(fontSize: 10),
        ),
        series: matrixData.entries.map((statusEntry) {
          final statusColor = widget.card.colorMapping[statusEntry.key] ?? AppColors.primary;

          return StackedColumnSeries<ChartDataPoint, String>(
            name: statusEntry.key,
            dataSource: statusEntry.value.entries.map((entry) =>
              ChartDataPoint(
                label: entry.key,
                value: entry.value,
                category: statusEntry.key,
              )
            ).toList(),
            xValueMapper: (ChartDataPoint data, _) => data.label,
            yValueMapper: (ChartDataPoint data, _) => data.value,
            color: statusColor,
            enableTooltip: true,
            dataLabelSettings: const DataLabelSettings(
              isVisible: true,
              labelPosition: ChartDataLabelPosition.inside,
              textStyle: TextStyle(fontSize: 9),
            ),
          );
        }).toList(),
        legend: const Legend(
          isVisible: true,
          position: LegendPosition.bottom,
          textStyle: TextStyle(fontSize: 10),
        ),
        tooltipBehavior: TooltipBehavior(enable: true),
      );
    } catch (e) {
      debugPrint('❌ خطأ في بناء المخطط المكدس: $e');
      return _buildColumnChart(); // fallback في حالة الخطأ
    }
  }

  /// بناء المخطط الشريطي المكدس
  Widget _buildStackedBarChart() {
    try {
      // تجميع البيانات حسب المستخدم والحالة للمخططات المكدسة
      final matrixData = _prepareMatrixData();

      if (matrixData.isEmpty) {
        debugPrint('⚠️ بيانات المصفوفة فارغة، استخدام المخطط العادي');
        return _buildBarChart(); // fallback للبيانات العادية
      }

      return SfCartesianChart(
        key: ValueKey('stacked_bar_${widget.card.id}_${widget.card.lastUpdated.millisecondsSinceEpoch}'),
        primaryXAxis: const NumericAxis(
          labelStyle: TextStyle(fontSize: 10),
        ),
        primaryYAxis: const CategoryAxis(
          labelStyle: TextStyle(fontSize: 10),
        ),
        series: matrixData.entries.map((statusEntry) {
          final statusColor = widget.card.colorMapping[statusEntry.key] ?? AppColors.primary;

          return StackedBarSeries<ChartDataPoint, String>(
            name: statusEntry.key,
            dataSource: statusEntry.value.entries.map((entry) =>
              ChartDataPoint(
                label: entry.key,
                value: entry.value,
                category: statusEntry.key,
              )
            ).toList(),
            xValueMapper: (ChartDataPoint data, _) => data.label,
            yValueMapper: (ChartDataPoint data, _) => data.value,
            color: statusColor,
            enableTooltip: true,
            dataLabelSettings: const DataLabelSettings(
              isVisible: true,
              labelPosition: ChartDataLabelPosition.inside,
              textStyle: TextStyle(fontSize: 9),
            ),
          );
        }).toList(),
        legend: const Legend(
          isVisible: true,
          position: LegendPosition.bottom,
          textStyle: TextStyle(fontSize: 10),
        ),
        tooltipBehavior: TooltipBehavior(enable: true),
      );
    } catch (e) {
      debugPrint('❌ خطأ في بناء المخطط الشريطي المكدس: $e');
      return _buildBarChart(); // fallback في حالة الخطأ
    }
  }

  /// بناء المخطط النقطي
  Widget _buildScatterChart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      series: <ScatterSeries<ChartDataPoint, String>>[
        ScatterSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          pointColorMapper: (ChartDataPoint data, _) => _getPointColor(data),
          enableTooltip: true,
          markerSettings: const MarkerSettings(
            isVisible: true,
            height: 8,
            width: 8,
          ),
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  /// بناء مخطط الفقاعات
  Widget _buildBubbleChart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      series: <BubbleSeries<ChartDataPoint, String>>[
        BubbleSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          sizeValueMapper: (ChartDataPoint data, _) => data.value,
          pointColorMapper: (ChartDataPoint data, _) => _getPointColor(data),
          enableTooltip: true,
          opacity: 0.7,
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  /// بناء المخطط القمعي
  Widget _buildFunnelChart() {
    return SfFunnelChart(
      title: ChartTitle(text: ''),
      legend: const Legend(
        isVisible: true,
        position: LegendPosition.bottom,
        textStyle: TextStyle(fontSize: 12),
      ),
      series: FunnelSeries<ChartDataPoint, String>(
        dataSource: widget.card.data,
        xValueMapper: (ChartDataPoint data, _) => data.label,
        yValueMapper: (ChartDataPoint data, _) => data.value,
        pointColorMapper: (ChartDataPoint data, _) => _getPointColor(data),
        dataLabelSettings: const DataLabelSettings(
          isVisible: true,
          labelPosition: ChartDataLabelPosition.outside,
          textStyle: TextStyle(fontSize: 10),
        ),
        neckWidth: '30%',
        neckHeight: '20%',
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
    );
  }

  /// بناء المخطط الهرمي
  Widget _buildPyramidChart() {
    return SfPyramidChart(
      title: ChartTitle(text: ''),
      legend: const Legend(
        isVisible: true,
        position: LegendPosition.bottom,
        textStyle: TextStyle(fontSize: 12),
      ),
      series: PyramidSeries<ChartDataPoint, String>(
        dataSource: widget.card.data,
        xValueMapper: (ChartDataPoint data, _) => data.label,
        yValueMapper: (ChartDataPoint data, _) => data.value,
        pointColorMapper: (ChartDataPoint data, _) => _getPointColor(data),
        dataLabelSettings: const DataLabelSettings(
          isVisible: true,
          labelPosition: ChartDataLabelPosition.outside,
          textStyle: TextStyle(fontSize: 10),
        ),
        pyramidMode: PyramidMode.linear,
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
    );
  }

  /// بناء المخطط الخطي المتدرج
  Widget _buildStepLineChart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      series: <StepLineSeries<ChartDataPoint, String>>[
        StepLineSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          color: AppColors.primary,
          width: 2,
          enableTooltip: true,
          markerSettings: const MarkerSettings(
            isVisible: true,
            height: 6,
            width: 6,
          ),
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  /// بناء المخطط المساحي المكدس
  Widget _buildStackedAreaChart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      series: <StackedAreaSeries<ChartDataPoint, String>>[
        StackedAreaSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          color: AppColors.primary.withValues(alpha: 0.7),
          enableTooltip: true,
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  /// بناء المخطط المساحي المكدس 100%
  Widget _buildStackedArea100Chart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
        maximum: 100,
      ),
      series: <StackedArea100Series<ChartDataPoint, String>>[
        StackedArea100Series<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          color: AppColors.primary.withValues(alpha: 0.7),
          enableTooltip: true,
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  /// بناء المخطط الأعمدة المكدسة 100%
  Widget _buildStackedColumn100Chart() {
    try {
      // تجميع البيانات حسب المستخدم والحالة للمخططات المكدسة
      final matrixData = _prepareMatrixData();

      if (matrixData.isEmpty) {
        debugPrint('⚠️ بيانات المصفوفة فارغة، استخدام المخطط العادي');
        return _buildColumnChart(); // fallback للبيانات العادية
      }

      return SfCartesianChart(
        key: ValueKey('stacked_column_100_${widget.card.id}_${widget.card.lastUpdated.millisecondsSinceEpoch}'),
        primaryXAxis: const CategoryAxis(
          labelStyle: TextStyle(fontSize: 10),
        ),
        primaryYAxis: const NumericAxis(
          labelStyle: TextStyle(fontSize: 10),
          maximum: 100,
        ),
        series: matrixData.entries.map((statusEntry) {
          final statusColor = widget.card.colorMapping[statusEntry.key] ?? AppColors.primary;

          return StackedColumn100Series<ChartDataPoint, String>(
            name: statusEntry.key,
            dataSource: statusEntry.value.entries.map((entry) =>
              ChartDataPoint(
                label: entry.key,
                value: entry.value,
                category: statusEntry.key,
              )
            ).toList(),
            xValueMapper: (ChartDataPoint data, _) => data.label,
            yValueMapper: (ChartDataPoint data, _) => data.value,
            color: statusColor,
            enableTooltip: true,
            dataLabelSettings: const DataLabelSettings(
              isVisible: true,
              labelPosition: ChartDataLabelPosition.inside,
              textStyle: TextStyle(fontSize: 9),
            ),
          );
        }).toList(),
        legend: const Legend(
          isVisible: true,
          position: LegendPosition.bottom,
          textStyle: TextStyle(fontSize: 10),
        ),
        tooltipBehavior: TooltipBehavior(enable: true),
        zoomPanBehavior: ZoomPanBehavior(
          enablePinching: true,
          enablePanning: true,
          enableDoubleTapZooming: true,
        ),
      );
    } catch (e) {
      debugPrint('❌ خطأ في بناء المخطط المكدس 100%: $e');
      return _buildColumnChart(); // fallback في حالة الخطأ
    }
  }

  /// بناء المخطط الشريطي المكدس 100%
  Widget _buildStackedBar100Chart() {
    try {
      // تجميع البيانات حسب المستخدم والحالة للمخططات المكدسة
      final matrixData = _prepareMatrixData();

      if (matrixData.isEmpty) {
        debugPrint('⚠️ بيانات المصفوفة فارغة، استخدام المخطط العادي');
        return _buildBarChart(); // fallback للبيانات العادية
      }

      return SfCartesianChart(
        key: ValueKey('stacked_bar_100_${widget.card.id}_${widget.card.lastUpdated.millisecondsSinceEpoch}'),
        primaryXAxis: const NumericAxis(
          labelStyle: TextStyle(fontSize: 10),
          maximum: 100,
        ),
        primaryYAxis: const CategoryAxis(
          labelStyle: TextStyle(fontSize: 10),
        ),
        series: matrixData.entries.map((statusEntry) {
          final statusColor = widget.card.colorMapping[statusEntry.key] ?? AppColors.primary;

          return StackedBar100Series<ChartDataPoint, String>(
            name: statusEntry.key,
            dataSource: statusEntry.value.entries.map((entry) =>
              ChartDataPoint(
                label: entry.key,
                value: entry.value,
                category: statusEntry.key,
              )
            ).toList(),
            xValueMapper: (ChartDataPoint data, _) => data.label,
            yValueMapper: (ChartDataPoint data, _) => data.value,
            color: statusColor,
            enableTooltip: true,
            dataLabelSettings: const DataLabelSettings(
              isVisible: true,
              labelPosition: ChartDataLabelPosition.inside,
              textStyle: TextStyle(fontSize: 9),
            ),
          );
        }).toList(),
        legend: const Legend(
          isVisible: true,
          position: LegendPosition.bottom,
          textStyle: TextStyle(fontSize: 10),
        ),
        tooltipBehavior: TooltipBehavior(enable: true),
        zoomPanBehavior: ZoomPanBehavior(
          enablePinching: true,
          enablePanning: true,
          enableDoubleTapZooming: true,
        ),
      );
    } catch (e) {
      debugPrint('❌ خطأ في بناء المخطط الشريطي المكدس 100%: $e');
      return _buildBarChart(); // fallback في حالة الخطأ
    }
  }

  /// بناء المخطط المساحي المنحني
  Widget _buildSplineAreaChart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      series: <SplineAreaSeries<ChartDataPoint, String>>[
        SplineAreaSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          color: AppColors.primary.withValues(alpha: 0.7),
          enableTooltip: true,
          markerSettings: const MarkerSettings(
            isVisible: true,
            height: 6,
            width: 6,
          ),
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  /// بناء المخطط المساحي المتدرج
  Widget _buildStepAreaChart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      series: <StepAreaSeries<ChartDataPoint, String>>[
        StepAreaSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          color: AppColors.primary.withValues(alpha: 0.7),
          enableTooltip: true,
          borderWidth: 2,
          borderColor: AppColors.primary,
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  /// بناء المخطط الأعمدة النطاق
  Widget _buildRangeColumnChart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      series: <RangeColumnSeries<ChartDataPoint, String>>[
        RangeColumnSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          lowValueMapper: (ChartDataPoint data, _) => data.value * 0.8,
          highValueMapper: (ChartDataPoint data, _) => data.value,
          pointColorMapper: (ChartDataPoint data, _) => _getPointColor(data),
          enableTooltip: true,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.outside,
            textStyle: TextStyle(fontSize: 10),
          ),
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  /// بناء المخطط المساحي النطاق
  Widget _buildRangeAreaChart() {
    try {
      return SfCartesianChart(
        key: ValueKey('range_area_${widget.card.id}_${widget.card.lastUpdated.millisecondsSinceEpoch}'),
        primaryXAxis: const CategoryAxis(
          labelStyle: TextStyle(fontSize: 10),
        ),
        primaryYAxis: const NumericAxis(
          labelStyle: TextStyle(fontSize: 10),
        ),
        series: <RangeAreaSeries<ChartDataPoint, String>>[
          RangeAreaSeries<ChartDataPoint, String>(
            dataSource: widget.card.data,
            xValueMapper: (ChartDataPoint data, _) => data.label,
            lowValueMapper: (ChartDataPoint data, _) => data.value * 0.8,
            highValueMapper: (ChartDataPoint data, _) => data.value,
            color: AppColors.primary.withValues(alpha: 0.7),
            enableTooltip: true,
            borderWidth: 2,
            borderColor: AppColors.primary,
          ),
        ],
        tooltipBehavior: TooltipBehavior(enable: true),
        zoomPanBehavior: ZoomPanBehavior(
          enablePinching: true,
          enablePanning: true,
          enableDoubleTapZooming: true,
        ),
      );
    } catch (e) {
      debugPrint('❌ خطأ في بناء المخطط المساحي النطاق: $e');
      return _buildAreaChart(); // fallback في حالة الخطأ
    }
  }

  /// بناء المخطط الشلال
  Widget _buildWaterfallChart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      series: <WaterfallSeries<ChartDataPoint, String>>[
        WaterfallSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          enableTooltip: true,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.outside,
            textStyle: TextStyle(fontSize: 10),
          ),
          color: AppColors.primary,
          negativePointsColor: Colors.red,
          intermediateSumColor: Colors.orange,
          totalSumColor: Colors.green,
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  /// بناء المخطط الشريطي الدائري
  Widget _buildRadialBarChart() {
    return SfCircularChart(
      title: ChartTitle(text: ''),
      legend: const Legend(
        isVisible: true,
        position: LegendPosition.bottom,
        textStyle: TextStyle(fontSize: 12),
      ),
      series: <RadialBarSeries<ChartDataPoint, String>>[
        RadialBarSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          pointColorMapper: (ChartDataPoint data, _) => _getPointColor(data),
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.outside,
            textStyle: TextStyle(fontSize: 10),
          ),
          enableTooltip: true,
          maximumValue: 100,
          radius: '80%',
          innerRadius: '40%',
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
    );
  }

  /// بناء المخطط الخطي السريع
  Widget _buildFastLineChart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      primaryYAxis: const NumericAxis(
        labelStyle: TextStyle(fontSize: 10),
      ),
      series: <FastLineSeries<ChartDataPoint, String>>[
        FastLineSeries<ChartDataPoint, String>(
          dataSource: widget.card.data,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          color: AppColors.primary,
          width: 2,
          enableTooltip: true,
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }



  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
            Icon(
              Icons.bar_chart,
              size: 48,
              color: AppColors.textSecondary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 12),
            Text(
              'لا توجد بيانات للعرض',
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 32,
              child: ElevatedButton.icon(
                onPressed: widget.onRefresh,
                icon: const Icon(Icons.refresh, size: 16),
                label: const Text('تحديث', style: TextStyle(fontSize: 12)),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// بناء مقبض تغيير الحجم
  Widget _buildResizeHandle() {
    return GestureDetector(
      onPanStart: (_) => _isResizing = true,
      onPanUpdate: (details) {
        if (_isResizing) {
          setState(() {
            _currentSize = Size(
              (_currentSize.width + details.delta.dx).clamp(300.0, 800.0),
              (_currentSize.height + details.delta.dy).clamp(200.0, 600.0),
            );
          });
        }
      },
      onPanEnd: (_) {
        _isResizing = false;
        if (widget.onSizeChanged != null) {
          widget.onSizeChanged!(_currentSize);
        }
      },
      child: Container(
        height: 20,
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(12),
            bottomRight: Radius.circular(12),
          ),
        ),
        child: Center(
          child: Container(
            width: 30,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        ),
      ),
    );
  }

  /// الحصول على أيقونة نوع المخطط
  IconData _getChartTypeIcon(ChartType type) {
    switch (type) {
      case ChartType.pie:
        return Icons.pie_chart;
      case ChartType.doughnut:
        return Icons.donut_small;
      case ChartType.column:
        return Icons.bar_chart;
      case ChartType.bar:
        return Icons.horizontal_rule;
      case ChartType.line:
        return Icons.show_chart;
      case ChartType.area:
        return Icons.area_chart;
      case ChartType.spline:
        return Icons.timeline;
      case ChartType.stackedColumn:
        return Icons.stacked_bar_chart;
      case ChartType.stackedBar:
        return Icons.view_stream;
      case ChartType.scatter:
        return Icons.scatter_plot;
      case ChartType.bubble:
        return Icons.bubble_chart;
      case ChartType.funnel:
        return Icons.filter_list;
      case ChartType.pyramid:
        return Icons.change_history;
      case ChartType.stepLine:
        return Icons.stairs;
      case ChartType.stackedArea:
        return Icons.stacked_line_chart;
      case ChartType.stackedArea100:
        return Icons.percent;
      case ChartType.stackedColumn100:
        return Icons.view_column;
      case ChartType.stackedBar100:
        return Icons.view_stream;
      case ChartType.splineArea:
        return Icons.waves;
      case ChartType.stepArea:
        return Icons.terrain;
      case ChartType.rangeColumn:
        return Icons.view_agenda;
      case ChartType.rangeArea:
        return Icons.landscape;
      case ChartType.waterfall:
        return Icons.waterfall_chart;
      case ChartType.fastLine:
        return Icons.speed;
      case ChartType.radialBar:
        return Icons.donut_large;
      default:
        return Icons.bar_chart;
    }
  }

  /// الحصول على اسم نوع المخطط
  String _getChartTypeName(ChartType type) {
    switch (type) {
      case ChartType.pie:
        return 'دائري';
      case ChartType.doughnut:
        return 'دونات';
      case ChartType.column:
        return 'أعمدة';
      case ChartType.bar:
        return 'شريطي';
      case ChartType.line:
        return 'خطي';
      case ChartType.area:
        return 'مساحي';
      case ChartType.spline:
        return 'منحني';
      case ChartType.stackedColumn:
        return 'أعمدة مكدسة';
      case ChartType.stackedBar:
        return 'شريطي مكدس';
      case ChartType.scatter:
        return 'نقطي';
      case ChartType.bubble:
        return 'فقاعي';
      case ChartType.funnel:
        return 'قمعي';
      case ChartType.pyramid:
        return 'هرمي';
      case ChartType.stepLine:
        return 'خطي متدرج';
      case ChartType.stackedArea:
        return 'مساحي مكدس';
      case ChartType.stackedArea100:
        return 'مساحي مكدس 100%';
      case ChartType.stackedColumn100:
        return 'أعمدة مكدسة 100%';
      case ChartType.stackedBar100:
        return 'شريطي مكدس 100%';
      case ChartType.splineArea:
        return 'مساحي منحني';
      case ChartType.stepArea:
        return 'مساحي متدرج';
      case ChartType.rangeColumn:
        return 'أعمدة نطاق';
      case ChartType.rangeArea:
        return 'مساحي نطاق';
      case ChartType.waterfall:
        return 'شلال';
      case ChartType.fastLine:
        return 'خطي سريع';
      case ChartType.radialBar:
        return 'شريطي دائري';
      default:
        return 'غير معروف';
    }
  }

  /// تنسيق وقت آخر تحديث
  String _formatLastUpdated() {
    final now = DateTime.now();
    final difference = now.difference(widget.card.lastUpdated);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  /// عرض المخطط بملء الشاشة في dialog
  void _showFullscreenChart() {
    // متغيرات لتتبع التغييرات داخل الـ Dialog
    ChartType currentChartType = widget.card.chartType;
    Map<String, Color> currentColorMapping = Map.from(widget.card.colorMapping);

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {

            return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(8),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.95,
            height: MediaQuery.of(context).size.height * 0.95,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              children: [
                // رأس الـ Dialog مع محدد نوع المخطط
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          widget.card.title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      // محدد نوع المخطط
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<ChartType>(
                            value: currentChartType,
                            icon: const Icon(Icons.keyboard_arrow_down,
                                            size: 16, color: Colors.white),
                            dropdownColor: AppColors.primary,
                            style: const TextStyle(color: Colors.white, fontSize: 12),
                            items: widget.card.supportedChartTypes.map((type) {
                              return DropdownMenuItem<ChartType>(
                                value: type,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(_getChartTypeIcon(type),
                                         size: 14, color: Colors.white),
                                    const SizedBox(width: 4),
                                    Text(
                                      _getChartTypeName(type),
                                      style: const TextStyle(color: Colors.white, fontSize: 12),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                            onChanged: (ChartType? newType) {
                              if (newType != null) {
                                // تحديث المخطط داخل الـ Dialog
                                setDialogState(() {
                                  currentChartType = newType;
                                });
                                // تحديث البطاقة الأصلية أيضاً
                                widget.onChartTypeChanged?.call(newType);
                              }
                            },
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // زر تخصيص الألوان
                      IconButton(
                        icon: const Icon(Icons.palette, color: Colors.white),
                        onPressed: () => _showColorCustomizationInDialog(
                          context,
                          currentColorMapping,
                          setDialogState,
                          (newColors) {
                            setDialogState(() {
                              currentColorMapping = newColors;
                            });
                            // تحديث البطاقة الأصلية
                            final controller = Get.find<DashboardController>();
                            controller.updateCardColors(widget.card.id, newColors);
                          }
                        ),
                        tooltip: 'تخصيص الألوان',
                      ),
                      // زر الفلاتر
                      IconButton(
                        icon: Icon(
                          Icons.filter_list,
                          color: widget.card.cardFilters.isNotEmpty
                              ? Colors.yellow
                              : Colors.white,
                        ),
                        onPressed: () => _showCardFiltersInDialog(context),
                        tooltip: 'فلاتر البطاقة',
                      ),
                      // زر التصدير
                      IconButton(
                        icon: const Icon(Icons.download, color: Colors.white),
                        onPressed: () => _showExportOptionsInDialog(context),
                        tooltip: 'تصدير البطاقة',
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white,
                        ),
                        tooltip: 'إغلاق',
                      ),
                    ],
                  ),
                ),
                // محتوى المخطط
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: widget.card.data.isEmpty
                        ? _buildEmptyState()
                        : _buildChartWithTypeAndColors(currentChartType, currentColorMapping),
                  ),
                ),
                // شريط الأدوات السفلي
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.background,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(16),
                      bottomRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'آخر تحديث: ${_formatLastUpdated()}',
                        style: AppStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Row(
                        children: [
                          IconButton(
                            onPressed: widget.onRefresh,
                            icon: const Icon(Icons.refresh),
                            tooltip: 'تحديث البيانات',
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton.icon(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(Icons.check, color: AppColors.white),
                            label: const Text('تم'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
          },
        );
      },
    );
  }

  /// عرض تخصيص الألوان
  void _showColorCustomization() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(24),
          child: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.8,
              maxHeight: MediaQuery.of(context).size.height * 0.7,
            ),
            child: ColorCustomizationWidget(
              card: widget.card,
              onColorsChanged: (newColors) {
                final controller = Get.find<DashboardController>();
                controller.updateCardColors(widget.card.id, newColors);
              },
            ),
          ),
        );
      },
    );
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _showExportOptions();
        break;
      case 'settings':
        // عرض إعدادات البطاقة
        break;
    }
  }

  /// عرض خيارات التصدير
  void _showExportOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير البطاقة كصورة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.image,
              size: 64,
              color: Colors.blue,
            ),
            const SizedBox(height: 16),
            const Text(
              'سيتم تصدير البطاقة كصورة PNG',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'البطاقة: ${widget.card.title}',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _exportCard(CardExportFormat.image);
            },
            icon: const Icon(Icons.download),
            label: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  /// عرض نافذة فلاتر البطاقة (النظام الجديد الموحد)
  void _showCardFilters() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SizedBox(
          width: 600,
          child: UnifiedCardFilterWidget(
            card: widget.card,
            onFilterApplied: () {
              // تحديث البطاقة بعد تطبيق الفلاتر (اختياري)
            },
          ),
        ),
      ),
    );
  }

  /// بناء المخطط بنوع وألوان محددة (للاستخدام في الـ Dialog)
  Widget _buildChartWithTypeAndColors(ChartType chartType, Map<String, Color> colorMapping) {
    // إنشاء بطاقة مؤقتة مع النوع والألوان الجديدة
    final tempCard = widget.card.copyWith(
      chartType: chartType,
      colorMapping: colorMapping,
    );

    // عرض المخطط فقط بدون إطار البطاقة
    return ChartOnlyWidget(card: tempCard);
  }

  /// عرض تخصيص الألوان داخل الـ Dialog
  void _showColorCustomizationInDialog(
    BuildContext context,
    Map<String, Color> currentColors,
    StateSetter setDialogState,
    Function(Map<String, Color>) onColorsChanged,
  ) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(24),
          child: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.8,
              maxHeight: MediaQuery.of(context).size.height * 0.7,
            ),
            child: ColorCustomizationWidget(
              card: widget.card.copyWith(colorMapping: currentColors),
              onColorsChanged: (newColors) {
                // تحديث الألوان في الـ Dialog الرئيسي
                onColorsChanged(newColors);
                // إغلاق نافذة تخصيص الألوان
                Navigator.of(context).pop();
              },
            ),
          ),
        );
      },
    );
  }

  /// عرض فلاتر البطاقة داخل الـ Dialog
  void _showCardFiltersInDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SizedBox(
          width: 600,
          child: UnifiedCardFilterWidget(
            card: widget.card,
            onFilterApplied: () {
              // تحديث البطاقة بعد تطبيق الفلاتر
              // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
            },
          ),
        ),
      ),
    );
  }

  /// عرض خيارات التصدير داخل الـ Dialog
  void _showExportOptionsInDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير البطاقة كصورة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.image,
              size: 64,
              color: Colors.blue,
            ),
            const SizedBox(height: 16),
            const Text(
              'سيتم تصدير البطاقة كصورة PNG',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'البطاقة: ${widget.card.title}',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _exportCard(CardExportFormat.image);
            },
            icon: const Icon(Icons.download),
            label: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  /// تصدير البطاقة
  Future<void> _exportCard(CardExportFormat format) async {
    try {
      // عرض مؤشر التحميل
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      final exportService = Get.find<DashboardCardExportService>();
      final filePath = await exportService.exportSingleCard(
        widget.card,
        format,
        includeChart: true,
        includeData: true,
        includeMetadata: true,
      );

      // إغلاق مؤشر التحميل
      Get.back();

      if (filePath != null) {
        // عرض خيارات ما بعد التصدير
        _showPostExportOptions(filePath);
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في تصدير البطاقة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (Get.isDialogOpen ?? false) Get.back();

      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء التصدير: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// عرض خيارات ما بعد التصدير
  void _showPostExportOptions(String filePath) {
    final exportService = Get.find<DashboardCardExportService>();
    final fileInfo = exportService.getFileInfo(filePath);

    Get.dialog(
      AlertDialog(
        title: const Text('تم التصدير بنجاح'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('اسم الملف: ${fileInfo['fileName']}'),
            Text('النوع: ${fileInfo['extension']}'),
            Text('الحجم: ${fileInfo['sizeFormatted']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              exportService.openExportedFile(filePath);
            },
            child: const Text('فتح الملف'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              exportService.shareExportedFile(
                filePath,
                subject: 'تقرير ${widget.card.title}',
              );
            },
            child: const Text('مشاركة'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

}
