import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:open_file/open_file.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../models/dashboard_models.dart';

/// أنواع التصدير المدعومة للبطاقات
enum CardExportFormat { image, pdf }

/// خدمة تصدير بطاقات لوحة التحكم كصور فقط
class DashboardCardExportService extends GetxService {
  
  /// تصدير بطاقة واحدة
  Future<String?> exportSingleCard(
    ChartCardModel card,
    CardExportFormat format, {
    bool includeChart = true,
    bool includeData = true,
    bool includeMetadata = true,
  }) async {
    try {
      switch (format) {
        case CardExportFormat.image:
          return await _exportCardToImage(card);
        case CardExportFormat.pdf:
          return await _exportCardToPdf(card);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تصدير البطاقة ${card.id}: $e');
      return null;
    }
  }

  /// تصدير عدة بطاقات (غير مدعوم للصور)
  Future<String?> exportMultipleCards(
    List<ChartCardModel> cards,
    CardExportFormat format, {
    String? customTitle,
    bool includeCharts = true,
    bool includeData = true,
    bool includeMetadata = true,
  }) async {
    debugPrint('❌ تصدير متعدد البطاقات غير مدعوم للصور');
    Get.snackbar(
      'غير مدعوم',
      'تصدير عدة بطاقات غير متاح للصور. يرجى تصدير كل بطاقة على حدة.',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange,
      colorText: Colors.white,
    );
    return null;
  }

  /// تصدير لوحة التحكم كاملة (غير مدعوم للصور)
  Future<String?> exportFullDashboard(
    List<ChartCardModel> cards, {
    String title = 'تقرير لوحة التحكم الشامل',
    CardExportFormat format = CardExportFormat.image,
    bool includeSummary = true,
    bool includeCharts = true,
    bool includeData = true,
  }) async {
    debugPrint('❌ تصدير لوحة التحكم الكاملة غير مدعوم للصور');
    Get.snackbar(
      'غير مدعوم',
      'تصدير لوحة التحكم الكاملة غير متاح للصور. يرجى تصدير كل بطاقة على حدة.',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange,
      colorText: Colors.white,
    );
    return null;
  }

  /// تصدير بطاقة إلى صورة
  Future<String?> _exportCardToImage(ChartCardModel card) async {
    try {
      debugPrint('🖼️ بدء تصدير الصورة للبطاقة ${card.id}');

      // إنشاء مسار الملف
      final directory = await getApplicationDocumentsDirectory();
      final imagePath = '${directory.path}/card_${card.id}_${DateTime.now().millisecondsSinceEpoch}.png';

      // إنشاء صورة بسيطة للبطاقة
      final imageBytes = await _createCardImage(card);

      // حفظ الصورة
      final file = File(imagePath);
      await file.writeAsBytes(imageBytes);

      Get.snackbar(
        'تم التصدير',
        'تم حفظ صورة البطاقة بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );

      debugPrint('✅ تم تصدير الصورة: $imagePath');
      return imagePath;

    } catch (e) {
      debugPrint('❌ خطأ في تصدير الصورة للبطاقة ${card.id}: $e');
      return null;
    }
  }

  /// إنشاء صورة للبطاقة
  Future<Uint8List> _createCardImage(ChartCardModel card) async {
    // إنشاء Canvas لرسم البطاقة
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = const Size(800, 600);

    // خلفية البطاقة
    final backgroundPaint = Paint()..color = Colors.white;
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), backgroundPaint);

    // رسم إطار البطاقة
    final borderPaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(10, 10, size.width - 20, size.height - 20),
        const Radius.circular(12),
      ),
      borderPaint,
    );

    // رسم عنوان البطاقة
    final titlePainter = TextPainter(
      text: TextSpan(
        text: card.title,
        style: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    titlePainter.layout(maxWidth: size.width - 40);
    titlePainter.paint(canvas, const Offset(20, 30));

    // رسم وصف البطاقة
    final descriptionPainter = TextPainter(
      text: TextSpan(
        text: card.description,
        style: const TextStyle(
          fontSize: 16,
          color: Colors.grey,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    descriptionPainter.layout(maxWidth: size.width - 40);
    descriptionPainter.paint(canvas, const Offset(20, 70));

    // رسم معلومات البيانات
    double yOffset = 120;
    final dataPainter = TextPainter(
      text: TextSpan(
        text: 'عدد نقاط البيانات: ${card.data.length}',
        style: const TextStyle(
          fontSize: 14,
          color: Colors.black87,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    dataPainter.layout();
    dataPainter.paint(canvas, Offset(20, yOffset));

    yOffset += 30;
    final typePainter = TextPainter(
      text: TextSpan(
        text: 'نوع المخطط: ${_getChartTypeName(card.chartType)}',
        style: const TextStyle(
          fontSize: 14,
          color: Colors.black87,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    typePainter.layout();
    typePainter.paint(canvas, Offset(20, yOffset));

    // رسم البيانات
    yOffset += 50;
    if (card.data.isNotEmpty) {
      final dataHeaderPainter = TextPainter(
        text: const TextSpan(
          text: 'البيانات:',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      dataHeaderPainter.layout();
      dataHeaderPainter.paint(canvas, Offset(20, yOffset));

      yOffset += 35;

      // رسم كل نقطة بيانات
      for (int i = 0; i < card.data.length && i < 10; i++) {
        final point = card.data[i];

        // رسم دائرة ملونة
        final colorPaint = Paint()..color = point.color ?? Colors.blue;
        canvas.drawCircle(Offset(30, yOffset + 8), 6, colorPaint);

        // رسم النص
        final pointPainter = TextPainter(
          text: TextSpan(
            text: '${point.label}: ${point.value.toStringAsFixed(1)}',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
          textDirection: TextDirection.ltr,
        );
        pointPainter.layout();
        pointPainter.paint(canvas, Offset(50, yOffset));

        yOffset += 25;
      }

      // إذا كان هناك المزيد من البيانات
      if (card.data.length > 10) {
        final morePainter = TextPainter(
          text: TextSpan(
            text: '... و ${card.data.length - 10} نقاط أخرى',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
              fontStyle: FontStyle.italic,
            ),
          ),
          textDirection: TextDirection.ltr,
        );
        morePainter.layout();
        morePainter.paint(canvas, Offset(20, yOffset));
      }
    }

    // رسم تاريخ التصدير في الأسفل
    final datePainter = TextPainter(
      text: TextSpan(
        text: 'تم التصدير في: ${DateTime.now().toString().substring(0, 19)}',
        style: const TextStyle(
          fontSize: 12,
          color: Colors.grey,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    datePainter.layout();
    datePainter.paint(canvas, Offset(20, size.height - 40));

    // تحويل إلى صورة
    final picture = recorder.endRecording();
    final image = await picture.toImage(size.width.toInt(), size.height.toInt());
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    return byteData!.buffer.asUint8List();
  }

  /// تصدير بطاقة إلى PDF
  Future<String?> _exportCardToPdf(ChartCardModel card) async {
    try {
      debugPrint('📄 بدء تصدير PDF للبطاقة ${card.id}');

      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // عنوان البطاقة
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(16),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.blue100,
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        card.title,
                        style: pw.TextStyle(
                          fontSize: 18,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 8),
                      pw.Text(
                        card.description,
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // معلومات البطاقة
                pw.Table(
                  border: pw.TableBorder.all(),
                  children: [
                    pw.TableRow(children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Chart Type', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(_getChartTypeName(card.chartType)),
                      ),
                    ]),
                    pw.TableRow(children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Data Points', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('${card.data.length}'),
                      ),
                    ]),
                    pw.TableRow(children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Last Updated', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(card.lastUpdated.toString().substring(0, 19)),
                      ),
                    ]),
                  ],
                ),

                pw.SizedBox(height: 20),

                // البيانات
                if (card.data.isNotEmpty) ...[
                  pw.Text(
                    'Data:',
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Table(
                    border: pw.TableBorder.all(),
                    children: [
                      pw.TableRow(
                        decoration: const pw.BoxDecoration(color: PdfColors.grey300),
                        children: [
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text('Label', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text('Value', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text('Category', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                          ),
                        ],
                      ),
                      ...card.data.map((point) => pw.TableRow(children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(point.label),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(point.value.toStringAsFixed(2)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(point.category ?? '-'),
                        ),
                      ])),
                    ],
                  ),
                ],

                pw.Spacer(),

                // تذييل
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(8),
                  decoration: const pw.BoxDecoration(
                    border: pw.Border(top: pw.BorderSide()),
                  ),
                  child: pw.Text(
                    'Exported on: ${DateTime.now().toString().substring(0, 19)}',
                    style: const pw.TextStyle(fontSize: 10),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ],
            );
          },
        ),
      );

      // حفظ PDF
      final directory = await getApplicationDocumentsDirectory();
      final pdfPath = '${directory.path}/card_${card.id}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File(pdfPath);
      await file.writeAsBytes(await pdf.save());

      Get.snackbar(
        'تم التصدير',
        'تم حفظ ملف PDF بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );

      debugPrint('✅ تم تصدير PDF: $pdfPath');
      return pdfPath;

    } catch (e) {
      debugPrint('❌ خطأ في تصدير PDF للبطاقة ${card.id}: $e');
      return null;
    }
  }

  /// الحصول على اسم نوع المخطط
  String _getChartTypeName(ChartType type) {
    switch (type) {
      case ChartType.pie: return 'دائري';
      case ChartType.column: return 'أعمدة';
      case ChartType.bar: return 'شريطي';
      case ChartType.line: return 'خطي';
      case ChartType.area: return 'مساحي';
      case ChartType.doughnut: return 'دونات';
      case ChartType.stackedColumn: return 'أعمدة مكدسة';
      case ChartType.stackedBar: return 'شريطي مكدس';
      default: return type.name;
    }
  }

  /// مشاركة الملف المصدر
  Future<void> shareExportedFile(String filePath, {String? subject}) async {
    try {
      await Share.shareXFiles(
        [XFile(filePath)],
        subject: subject ?? 'صورة من لوحة التحكم',
      );
    } catch (e) {
      debugPrint('❌ خطأ في مشاركة الملف: $e');
    }
  }

  /// فتح الملف المصدر
  Future<void> openExportedFile(String filePath) async {
    try {
      await OpenFile.open(filePath);
    } catch (e) {
      debugPrint('❌ خطأ في فتح الملف: $e');
    }
  }

  /// الحصول على معلومات الملف
  Map<String, dynamic> getFileInfo(String filePath) {
    final file = File(filePath);
    final fileName = file.path.split('/').last;
    final extension = fileName.split('.').last.toUpperCase();
    final size = file.lengthSync();
    
    return {
      'fileName': fileName,
      'extension': extension,
      'size': size,
      'sizeFormatted': _formatFileSize(size),
      'path': filePath,
    };
  }

  /// تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// حذف الملف المؤقت
  Future<bool> deleteExportedFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('❌ خطأ في حذف الملف: $e');
      return false;
    }
  }

  /// التحقق من وجود الملف
  Future<bool> fileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// الحصول على قائمة الملفات المصدرة
  Future<List<String>> getExportedFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final files = directory.listSync()
          .where((file) => file.path.contains('card_') && file.path.endsWith('.png'))
          .map((file) => file.path)
          .toList();
      
      // ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
      files.sort((a, b) => File(b).lastModifiedSync().compareTo(File(a).lastModifiedSync()));
      
      return files;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على قائمة الملفات: $e');
      return [];
    }
  }

  /// تنظيف الملفات القديمة (أكثر من 7 أيام)
  Future<int> cleanupOldFiles() async {
    try {
      final files = await getExportedFiles();
      final now = DateTime.now();
      int deletedCount = 0;
      
      for (final filePath in files) {
        final file = File(filePath);
        final lastModified = await file.lastModified();
        final daysDifference = now.difference(lastModified).inDays;
        
        if (daysDifference > 7) {
          await file.delete();
          deletedCount++;
        }
      }
      
      if (deletedCount > 0) {
        debugPrint('🧹 تم حذف $deletedCount ملف قديم');
      }
      
      return deletedCount;
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف الملفات القديمة: $e');
      return 0;
    }
  }

  /// إحصائيات التصدير
  Future<Map<String, dynamic>> getExportStatistics() async {
    try {
      final files = await getExportedFiles();
      int totalSize = 0;
      
      for (final filePath in files) {
        final file = File(filePath);
        if (await file.exists()) {
          totalSize += await file.length();
        }
      }
      
      return {
        'totalFiles': files.length,
        'totalSize': totalSize,
        'totalSizeFormatted': _formatFileSize(totalSize),
        'oldestFile': files.isNotEmpty ? files.last : null,
        'newestFile': files.isNotEmpty ? files.first : null,
      };
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على إحصائيات التصدير: $e');
      return {
        'totalFiles': 0,
        'totalSize': 0,
        'totalSizeFormatted': '0 B',
        'oldestFile': null,
        'newestFile': null,
      };
    }
  }
}
