import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:open_file/open_file.dart';
import '../models/dashboard_models.dart';

/// أنواع التصدير المدعومة للبطاقات
enum CardExportFormat { image, pdf }

/// خدمة تصدير بطاقات لوحة التحكم كصور فقط
class DashboardCardExportService extends GetxService {
  
  /// تصدير بطاقة واحدة كصورة
  Future<String?> exportSingleCard(
    ChartCardModel card, 
    CardExportFormat format, {
    bool includeChart = true,
    bool includeData = true,
    bool includeMetadata = true,
  }) async {
    try {
      return await _exportCardToImage(card);
    } catch (e) {
      debugPrint('❌ خطأ في تصدير البطاقة ${card.id}: $e');
      return null;
    }
  }

  /// تصدير عدة بطاقات (غير مدعوم للصور)
  Future<String?> exportMultipleCards(
    List<ChartCardModel> cards,
    CardExportFormat format, {
    String? customTitle,
    bool includeCharts = true,
    bool includeData = true,
    bool includeMetadata = true,
  }) async {
    debugPrint('❌ تصدير متعدد البطاقات غير مدعوم للصور');
    Get.snackbar(
      'غير مدعوم',
      'تصدير عدة بطاقات غير متاح للصور. يرجى تصدير كل بطاقة على حدة.',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange,
      colorText: Colors.white,
    );
    return null;
  }

  /// تصدير لوحة التحكم كاملة (غير مدعوم للصور)
  Future<String?> exportFullDashboard(
    List<ChartCardModel> cards, {
    String title = 'تقرير لوحة التحكم الشامل',
    CardExportFormat format = CardExportFormat.image,
    bool includeSummary = true,
    bool includeCharts = true,
    bool includeData = true,
  }) async {
    debugPrint('❌ تصدير لوحة التحكم الكاملة غير مدعوم للصور');
    Get.snackbar(
      'غير مدعوم',
      'تصدير لوحة التحكم الكاملة غير متاح للصور. يرجى تصدير كل بطاقة على حدة.',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange,
      colorText: Colors.white,
    );
    return null;
  }

  /// تصدير بطاقة إلى صورة
  Future<String?> _exportCardToImage(ChartCardModel card) async {
    try {
      debugPrint('🖼️ بدء تصدير الصورة للبطاقة ${card.id}');
      
      // TODO: تنفيذ تصدير الصورة الحقيقي باستخدام screenshot
      // هذا مثال أساسي - يحتاج تطوير أكثر
      
      // محاكاة عملية التصدير
      await Future.delayed(const Duration(seconds: 1));
      
      // إنشاء مسار الملف
      final directory = await getApplicationDocumentsDirectory();
      final imagePath = '${directory.path}/card_${card.id}_${DateTime.now().millisecondsSinceEpoch}.png';
      
      // محاكاة إنشاء ملف صورة (للاختبار)
      final file = File(imagePath);
      await file.writeAsString('صورة تجريبية للبطاقة: ${card.title}');
      
      Get.snackbar(
        'تم التصدير',
        'تم حفظ صورة البطاقة بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
      
      debugPrint('✅ تم تصدير الصورة: $imagePath');
      return imagePath;
      
    } catch (e) {
      debugPrint('❌ خطأ في تصدير الصورة للبطاقة ${card.id}: $e');
      return null;
    }
  }

  /// مشاركة الملف المصدر
  Future<void> shareExportedFile(String filePath, {String? subject}) async {
    try {
      await Share.shareXFiles(
        [XFile(filePath)],
        subject: subject ?? 'صورة من لوحة التحكم',
      );
    } catch (e) {
      debugPrint('❌ خطأ في مشاركة الملف: $e');
    }
  }

  /// فتح الملف المصدر
  Future<void> openExportedFile(String filePath) async {
    try {
      await OpenFile.open(filePath);
    } catch (e) {
      debugPrint('❌ خطأ في فتح الملف: $e');
    }
  }

  /// الحصول على معلومات الملف
  Map<String, dynamic> getFileInfo(String filePath) {
    final file = File(filePath);
    final fileName = file.path.split('/').last;
    final extension = fileName.split('.').last.toUpperCase();
    final size = file.lengthSync();
    
    return {
      'fileName': fileName,
      'extension': extension,
      'size': size,
      'sizeFormatted': _formatFileSize(size),
      'path': filePath,
    };
  }

  /// تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// حذف الملف المؤقت
  Future<bool> deleteExportedFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('❌ خطأ في حذف الملف: $e');
      return false;
    }
  }

  /// التحقق من وجود الملف
  Future<bool> fileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// الحصول على قائمة الملفات المصدرة
  Future<List<String>> getExportedFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final files = directory.listSync()
          .where((file) => file.path.contains('card_') && file.path.endsWith('.png'))
          .map((file) => file.path)
          .toList();
      
      // ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
      files.sort((a, b) => File(b).lastModifiedSync().compareTo(File(a).lastModifiedSync()));
      
      return files;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على قائمة الملفات: $e');
      return [];
    }
  }

  /// تنظيف الملفات القديمة (أكثر من 7 أيام)
  Future<int> cleanupOldFiles() async {
    try {
      final files = await getExportedFiles();
      final now = DateTime.now();
      int deletedCount = 0;
      
      for (final filePath in files) {
        final file = File(filePath);
        final lastModified = await file.lastModified();
        final daysDifference = now.difference(lastModified).inDays;
        
        if (daysDifference > 7) {
          await file.delete();
          deletedCount++;
        }
      }
      
      if (deletedCount > 0) {
        debugPrint('🧹 تم حذف $deletedCount ملف قديم');
      }
      
      return deletedCount;
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف الملفات القديمة: $e');
      return 0;
    }
  }

  /// إحصائيات التصدير
  Future<Map<String, dynamic>> getExportStatistics() async {
    try {
      final files = await getExportedFiles();
      int totalSize = 0;
      
      for (final filePath in files) {
        final file = File(filePath);
        if (await file.exists()) {
          totalSize += await file.length();
        }
      }
      
      return {
        'totalFiles': files.length,
        'totalSize': totalSize,
        'totalSizeFormatted': _formatFileSize(totalSize),
        'oldestFile': files.isNotEmpty ? files.last : null,
        'newestFile': files.isNotEmpty ? files.first : null,
      };
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على إحصائيات التصدير: $e');
      return {
        'totalFiles': 0,
        'totalSize': 0,
        'totalSizeFormatted': '0 B',
        'oldestFile': null,
        'newestFile': null,
      };
    }
  }
}
