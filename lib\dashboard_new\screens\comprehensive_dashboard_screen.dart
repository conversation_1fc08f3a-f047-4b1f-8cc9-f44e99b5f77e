import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/dashboard_controller.dart';

import '../widgets/dashboard_layout_manager.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';


/// شاشة لوحة التحكم الشاملة والاحترافية
class ComprehensiveDashboardScreen extends StatefulWidget {
  const ComprehensiveDashboardScreen({super.key});

  @override
  State<ComprehensiveDashboardScreen> createState() => _ComprehensiveDashboardScreenState();
}

class _ComprehensiveDashboardScreenState extends State<ComprehensiveDashboardScreen>
    with TickerProviderStateMixin {
  late final DashboardController _controller;
  late final TabController _tabController;


  bool _showFilters = false;
  int _gridColumns = 2;
  double _cardSpacing = 16.0;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<DashboardController>();
    _tabController = TabController(length: 3, vsync: this);

    // تحميل البيانات إذا لم تكن محملة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_controller.chartCards.isEmpty) {
        _controller.loadDashboardData(forceRefresh: true);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildTabBar(),
          // if (_showFilters)
          //   Container(
          //     constraints: BoxConstraints(
          //       maxHeight: MediaQuery.of(context).size.height * 0.3,
          //     ),
          //     child: SingleChildScrollView(
          //       child: _buildFiltersSection(),
          //     ),
          //   ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildChartsTab(),
                _buildAnalyticsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'لوحة التحكم الشاملة',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: AppColors.primary,
      elevation: 0,
     
    );
  }

  /// بناء شريط التبويبات
  Widget _buildTabBar() {
    return Container(
      color: AppColors.primary,
      child: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        indicatorWeight: 3,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        labelStyle: AppStyles.titleSmall.copyWith(fontWeight: FontWeight.w600),
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard),
            text: 'نظرة عامة',
          ),
          Tab(
            icon: Icon(Icons.bar_chart),
            text: 'المخططات',
          ),
          Tab(
            icon: Icon(Icons.analytics),
            text: 'التحليلات',
          ),
        ],
      ),
    );
  }

  // /// بناء قسم المرشحات
  // Widget _buildFiltersSection() {
  //   return Container(
  //     margin: const EdgeInsets.all(16),
  //     child: DashboardFilterWidget(
  //       onFiltersChanged: (filters) {
  //         // معالجة تغيير المرشحات
  //       },
  //     ),
  //   );
  // }

  /// بناء تبويب النظرة العامة
  Widget _buildOverviewTab() {
    return Obx(() {
      if (_controller.isLoading) {
        return _buildLoadingState();
      }

      if (_controller.error.isNotEmpty) {
        return _buildErrorState();
      }

      return RefreshIndicator(
        onRefresh: _controller.refreshDashboard,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatisticsCards(),
              const SizedBox(height: 24),
              _buildQuickInsights(),
            ],
          ),
        ),
      );
    });
  }

  /// بناء تبويب المخططات
  Widget _buildChartsTab() {
    return Obx(() {
      if (_controller.isLoading) {
        return _buildLoadingState();
      }

      if (_controller.error.isNotEmpty) {
        return _buildErrorState();
      }

      return RefreshIndicator(
        onRefresh: _controller.refreshDashboard,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: _buildChartsGrid(),
        ),
      );
    });
  }

  /// بناء تبويب التحليلات
  Widget _buildAnalyticsTab() {
    return Obx(() {
      if (_controller.isLoading) {
        return _buildLoadingState();
      }

      if (_controller.error.isNotEmpty) {
        return _buildErrorState();
      }

      return RefreshIndicator(
        onRefresh: _controller.refreshDashboard,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildAdvancedAnalytics(),
              const SizedBox(height: 24),
              _buildTrendAnalysis(),
            ],
          ),
        ),
      );
    });
  }

  /// بناء بطاقات الإحصائيات
  Widget _buildStatisticsCards() {
    final taskStats = _controller.taskStatistics;
    final userStats = _controller.userStatistics;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإحصائيات العامة',
          style: AppStyles.headingMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 10),
        GridView.count(
          crossAxisCount: _gridColumns,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: _cardSpacing,
          mainAxisSpacing: _cardSpacing,
          childAspectRatio: 2.5,
          children: [
            _buildStatCard(
              'إجمالي المهام',
              taskStats.totalTasks.toString(),
              Icons.assignment,
              AppColors.primary,
            ),
            _buildStatCard(
              'المهام المكتملة',
              taskStats.completedTasks.toString(),
              Icons.check_circle,
              Colors.green,
            ),
            _buildStatCard(
              'المهام قيد التنفيذ',
              taskStats.inProgressTasks.toString(),
              Icons.hourglass_empty,
              Colors.orange,
            ),
            _buildStatCard(
              'المهام المتأخرة',
              taskStats.overdueTasks.toString(),
              Icons.warning,
              Colors.red,
            ),
            _buildStatCard(
              'إجمالي المستخدمين',
              userStats.totalUsers.toString(),
              Icons.people,
              AppColors.accent,
            ),
            _buildStatCard(
              'المستخدمون النشطون',
              userStats.activeUsers.toString(),
              Icons.person,
              Colors.blue,
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [color.withValues(alpha: 0.1), color.withValues(alpha: 0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    value,
                    style: AppStyles.headingMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  Text(
                    title,
                    style: AppStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء الرؤى السريعة
  Widget _buildQuickInsights() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'رؤى سريعة',
          style: AppStyles.headingMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildInsightItem(
                  'معدل إنجاز المهام',
                  '${(_controller.taskStatistics.completedTasks / _controller.taskStatistics.totalTasks * 100).toStringAsFixed(1)}%',
                  Icons.trending_up,
                  Colors.green,
                ),
                const Divider(),
                _buildInsightItem(
                  'المهام المتأخرة',
                  '${_controller.taskStatistics.overdueTasks} مهمة',
                  Icons.schedule,
                  Colors.red,
                ),
                const Divider(),
                _buildInsightItem(
                  'المستخدمون النشطون',
                  '${(_controller.userStatistics.activeUsers / _controller.userStatistics.totalUsers * 100).toStringAsFixed(1)}%',
                  Icons.people,
                  Colors.blue,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// بناء عنصر رؤية
  Widget _buildInsightItem(String title, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: AppStyles.bodyMedium,
          ),
        ),
        Text(
          value,
          style: AppStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// بناء شبكة المخططات باستخدام مدير التخطيط المتقدم
  Widget _buildChartsGrid() {
    if (_controller.chartCards.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد مخططات للعرض',
              style: AppStyles.headlineSmall.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'تأكد من وجود بيانات في النظام',
              style: AppStyles.bodyMedium.copyWith(
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _controller.refreshDashboard(),
              icon: const Icon(Icons.refresh),
              label: const Text('تحديث البيانات'),
            ),
          ],
        ),
      );
    }

    // استخدام مدير التخطيط المتقدم مع Obx للتفاعل مع التحديثات
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.8, // تحديد ارتفاع مناسب
      child: Obx(() => DashboardLayoutManager(
        key: ValueKey('dashboard_layout_${_controller.chartCards.length}_${_controller.chartCards.map((c) => '${c.id}_${c.lastUpdated.millisecondsSinceEpoch}').join('_')}'),
        cards: _controller.chartCards,
        onCardsReordered: (reorderedCards) {
          _controller.reorderChartCards(reorderedCards);
        },
        onCardUpdated: (updatedCard) {
          _controller.updateCard(updatedCard);
        },
        onRefresh: () {
          _controller.refreshDashboard();
        },
        onRefreshSingle: (cardId) {
          _controller.refreshSingleCard(cardId);
        },
      )),
    );
  }

  /// بناء التحليلات المتقدمة
  Widget _buildAdvancedAnalytics() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التحليلات المتقدمة',
          style: AppStyles.headingMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'ستتم إضافة التحليلات المتقدمة هنا',
              style: AppStyles.bodyMedium,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء تحليل الاتجاهات
  Widget _buildTrendAnalysis() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تحليل الاتجاهات',
          style: AppStyles.headingMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'ستتم إضافة تحليل الاتجاهات هنا',
              style: AppStyles.bodyMedium,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري تحميل بيانات لوحة التحكم...'),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: AppStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            _controller.error,
            style: AppStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _controller.loadDashboardData(forceRefresh: true),
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء زر الإجراء العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () => _controller.refreshDashboard(),
      backgroundColor: AppColors.primary,
      tooltip: 'تحديث البيانات',
      child: const Icon(Icons.refresh, color: Colors.white),
    );
  }

  /// معالجة إعدادات العرض
  void _handleViewSettings(String value) {
    setState(() {
      switch (value) {
        case 'columns_1':
          _gridColumns = 1;
          break;
        case 'columns_2':
          _gridColumns = 2;
          break;
        case 'columns_3':
          _gridColumns = 3;
          break;
        case 'spacing':
          _showSpacingDialog();
          break;
      }
    });
  }

  /// عرض حوار تباعد البطاقات
  void _showSpacingDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تباعد البطاقات'),
        content: StatefulBuilder(
          builder: (context, setState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('التباعد الحالي: ${_cardSpacing.round()}px'),
              Slider(
                value: _cardSpacing,
                min: 8.0,
                max: 32.0,
                divisions: 6,
                onChanged: (value) {
                  setState(() {
                    _cardSpacing = value;
                  });
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {});
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }






}
